import { reactive } from 'vue';

interface ModalRef {
  close?: () => void;
}

const modalRefs = reactive<Record<string, ModalRef>>({});

export function useModalManager() {
  const registerModal = (name: string, ref: ModalRef) => {
    modalRefs[name] = ref;
  };

  const unregisterModal = (name: string) => {
    delete modalRefs[name];
  };

  const closeAllModals = () => {
    Object.values(modalRefs).forEach(modal => {
      modal.close?.();
    });
  };

  return {
    registerModal,
    unregisterModal,
    closeAllModals
  };
}