<template>
  <a-modal v-bind="$attrs" :open="modalStatus" @update:open="onUpdateOpen" @after-close="() => emit('after-close')"
    @ok="e => emit('ok', e)" @cancel="e => emit('cancel', e)">
    <template #title>
      <slot name="title">{{ $attrs.title }}</slot>
    </template>
    <template #footer v-if="$slots.footer || $attrs.footer !== undefined">
      <slot name="footer">
        <template v-if="$attrs.footer !== undefined">{{ $attrs.footer }}</template>
      </slot>
    </template>
    <template #closeIcon v-if="$slots.closeIcon">
      <slot name="closeIcon"></slot>
    </template>
    <slot></slot>
  </a-modal>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue';
import { useModalManager } from '@/hooks/useModalManager';

defineOptions({ name: 'BaseModal' });

const props = defineProps<{ modelValue?: boolean; open?: boolean; modalId?: string }>();
const emit = defineEmits(['update:modelValue', 'update:open', 'after-close', 'ok', 'cancel']);

const modalStatus = computed({
  get: () => props.open ?? props.modelValue ?? false,
  set: val => {
    emit('update:open', val);
    emit('update:modelValue', val);
  },
});
const onUpdateOpen = (val: boolean) => (modalStatus.value = val);

const { registerModal, unregisterModal } = useModalManager();
const modalId = props.modalId;

const closeModal = () => {
  emit('update:open', false);
  emit('update:modelValue', false);
};

onMounted(() => {
  if (modalId) {
    registerModal(modalId, { close: closeModal });
  }
  window.addEventListener('close-all-modals', closeModal);
});
onUnmounted(() => {
  if (modalId) {
    unregisterModal(modalId);
  }
  window.removeEventListener('close-all-modals', closeModal);
});
</script>
