{"name": "xdc-components", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"ant-design-vue": "^4.2.6", "lodash-es": "^4.17.21", "nanoid": "^5.1.5", "path": "^0.12.7", "pinia": "^2.0.0", "vue": "^3.5.17", "vxe-pc-ui": "^4.7.25", "vxe-table": "^4.14.6"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "less": "^4.4.0", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}, "peerDependencies": {"pinia": "^2.0.0"}, "description": "Vue 3 组件库，包含常用的业务组件。", "main": "index.js", "keywords": [], "author": "", "license": "ISC"}