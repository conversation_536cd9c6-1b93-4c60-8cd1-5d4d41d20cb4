{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": [
    "src/env.d.ts",
    "src/**/*.js",
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.d.ts",
    "src/**/*.vue",
    "tests/**/*.ts",
    "typings/**/*.d.ts",
    "typings/**/*.ts",
    "node_modules/ant-design-vue/typings/global.d.ts",
  ],
  "exclude": [
    "node_modules/ant-design-vue/lib/**",
    "dist",
    "src/**/__tests__/*"
  ],
      "compilerOptions": {
      "target": "esnext",
      "module": "esnext",
      "moduleResolution": "node",
      "allowJs": true,
      "isolatedModules": true,
      "composite": true,
      "baseUrl": ".",
      "strict": false,
      "noEmit": true,
      "skipLibCheck": true,
      "noImplicitAny": false,
      "noImplicitReturns": false,
      "noImplicitThis": false,
      "noUncheckedIndexedAccess": false,
      "exactOptionalPropertyTypes": false,
      "noPropertyAccessFromIndexSignature": false,
      "allowUnreachableCode": true,
      "allowUnusedLabels": true,
      "strictPropertyInitialization": false,
      "allowSyntheticDefaultImports": true,
      "sourceMap": true,
      "noUnusedLocals": false,
      "noUnusedParameters": false,
      "noFallthroughCasesInSwitch": false,
      "lib": [
        "esnext",
        "dom"
      ],
      "paths": {
        "@/*": [
          "src/*"
        ],
        "#/*": [
          "typings/*"
        ]
      }
    }
}