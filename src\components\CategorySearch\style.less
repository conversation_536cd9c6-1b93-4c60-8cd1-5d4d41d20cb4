.category-search {
    position: relative;
    width: 100%;
}

.search-wrapper {
    width: 100%;
    position: relative;
    display: flex;
    align-items: stretch;
}

.tag-list {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding-right: 8px;
    display: flex;
    flex-wrap: wrap;
}

.tag-list-inner {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;
    min-height: 24px;
    max-height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 2px 4px;
    width: 100%;
}

.tag-list-inner::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.tag-list-inner::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 3px;
}

.tag-list-inner::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 3px;
}

.tag-item {
    cursor: pointer;
    transition: all 0.3s;
    /* max-width: 300px; */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    user-select: none;
    margin: 0 2px;
    line-height: 28px;
    height: 28px;
}

.tag-item:hover {
    background: #e6f7ff;
    border-color: #91d5ff;
}

.dropdown-panel {
    position: absolute;
    top: 100%;
    left: 0;
    width: 400px;
    max-width: 100%;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    margin-top: 4px;
    z-index: 1050;
}

.type-list {
    max-height: 300px;
    overflow-y: auto;
}

.type-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.type-item:hover {
    background: #f5f5f5;
}

.form-content {
    width: 100%;
    padding: 16px;
}

.form-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 8px;
}

.form-item {
    width: 100%;
    margin-bottom: 16px;
}

.number-range {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

:deep(.ant-input-affix-wrapper) {
    padding: 2px 4px;
    min-height: 32px;
    height: auto;
    max-height: 38px;
    cursor: pointer;
    overflow-y: auto;
}

:deep(.ant-input-affix-wrapper::-webkit-scrollbar) {
    width: 6px;
    height: 6px;
}

:deep(.ant-input-affix-wrapper::-webkit-scrollbar-thumb) {
    background: #d9d9d9;
    border-radius: 3px;
}

:deep(.ant-input-affix-wrapper::-webkit-scrollbar-track) {
    background: #f5f5f5;
    border-radius: 3px;
}

/* 添加禁用状态样式 */
:deep(.ant-input-disabled) {
    cursor: not-allowed;
    background-color: #f5f5f5;
}

/* 优化操作按钮组样式 */

.action-group {
    display: flex;
    align-items: center;
    padding: 0 0 0 8px;
    border-radius: 0 2px 2px 0;
    color: #000;
}

/* 确保 popover 触发器不会导致位移 */
:deep(.ant-popover-trigger) {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* 调整输入框右侧内边距，避免与操作按钮组重叠 */
:deep(.ant-input-affix-wrapper) {
    padding-right: 8px;
}

:deep(.ant-input-prefix) {
    margin-right: 0;
    flex: 1;
    min-width: 0;
    max-width: calc(100% - 150px);
    position: relative;
    display: flex;
}

:deep(.ant-picker),
:deep(.ant-select) {
    width: 100%;
}

.column-setting-content {
    min-width: 250px;
    padding: 8px;
}

.column-list {
    margin-bottom: 16px;
}

.setting-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding-bottom: 14px;
}

.column-header {
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 1px;
}

.column-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
}

.column-title {
    flex: 1;
    margin: 0 8px;
}

.drag-handle {
    cursor: move;
    color: #000;
    transition: color 0.3s;
    font-size: 16px;
}

.drag-handle:hover {
    color: #1890ff;
}

.column-item {
    display: flex;
    padding: 8px 12px;
    background: #fafafa;
    margin-bottom: 8px;
    border-radius: 4px;
    transition: all 0.3s;
}

.column-item:hover {
    background: #f0f0f0;
}

.column-cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.visibility-icon {
    cursor: pointer;
    color: #000;
    font-size: 14px;
    transition: color 0.3s;
}

.visibility-icon:hover {
    color: #1890ff;
}

.visibility-icon.visible {
    color: #1890ff;
}

.visibility-icon.disabled {
    cursor: not-allowed;
    opacity: 0.3;
    pointer-events: none;
}

.column-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.action-icon {
    cursor: pointer;
    color: #666;
    font-size: 14px;
    transition: all 0.3s;
}

.action-icon:hover {
    color: #1890ff;
}

.action-icon.active {
    color: #1890ff;
    transform: scale(1.1);
}


.action-border {
    pointer-events: auto;
    margin-left: 8px;
    border: 1px solid #d9d9d9;
    padding: 6px 10px 4px 10px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 18px;
    transition: all 0.3s;
}

.action-border:hover {
    border: 1px solid #1890ff;
    color: #1890ff;
}

.action-border-disabled {
    pointer-events: none;
    background: #f5f5f5;
    color: #d9d9d9 !important;
    border: 1px solid #d9d9d9 !important;
}