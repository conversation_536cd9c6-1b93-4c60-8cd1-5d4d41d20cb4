import type { App } from 'vue'
import CategorySearch from './CategorySearch/index.vue'
import BaseModal from './BaseModal/index.vue'
import FileUpload from './FileUpload/index.vue'
import { useModalManager } from '@/hooks/useModalManager'

// 统一引入所有组件样式
import './CategorySearch/style.less'
// 后续添加更多组件样式
// import './OtherComponent/style.less'

// 组件列表
const components = [
    CategorySearch,
    BaseModal,
    FileUpload
    // 后续可以在这里添加更多组件
    // ExampleComponent,
    // AnotherComponent,
]

// 安装函数
const install = (app: App) => {
    components.forEach((component) => {
        app.component(component.name, component)
    })
}

// 导出所有组件
export {
    CategorySearch,
    BaseModal,
    FileUpload,
    useModalManager
    // 后续可以在这里导出更多组件
    // ExampleComponent,
    // AnotherComponent,
}

// 默认导出安装函数
export default { install } 