{"name": "xdc-components", "version": "2.0.0", "description": "常用组件库", "main": "xdc-components.js", "module": "xdc-components.js", "types": "xdc-components.d.ts", "files": ["xdc-components.js", "xdc-components.css", "xdc-components.umd.cjs", "xdc-components.d.ts"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["vue", "vue3", "components", "ui", "ant-design-vue", "vxe-table"], "author": "", "license": "ISC", "peerDependencies": {"vue": "^3.5.0", "pinia": "^2.0.0"}, "dependencies": {"ant-design-vue": "^4.2.6", "lodash-es": "^4.17.21", "vxe-pc-ui": "^4.7.25", "vxe-table": "^4.14.6"}}