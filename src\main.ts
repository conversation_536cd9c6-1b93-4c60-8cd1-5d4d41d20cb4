import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';

// 导入组件库
import XDCComponents from './components';
// 导入 pinia
import { createPinia } from 'pinia';
import VxeUITable from 'vxe-table';
import { VxeLoading, VxeTooltip } from 'vxe-pc-ui';

import 'vxe-pc-ui/lib/style.css';
import 'vxe-table/lib/style.css';
// 创建 Vue 应用
const app = createApp(App);

// 注册插件
app.use(createPinia()); // 添加 Pinia 状态管理
app.use(Antd);
app.use(XDCComponents);
app.use(VxeLoading);
app.use(VxeTooltip);
app.use(VxeUITable);
// 挂载应用
app.mount('#app');