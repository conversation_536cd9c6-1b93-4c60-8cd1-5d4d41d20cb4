import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url';
import * as path from "path";
import * as fs from 'fs';

// 自定义插件：构建后复制 package.json 和 README.md
const copyFiles = () => {
  return {
    name: 'copy-files',
    closeBundle() {
      try {
        // 复制 package.json
        const packageJsonPath = path.resolve(__dirname, 'package.json');
        const targetPackagePath = path.resolve(__dirname, 'xdc-components/package.json');

        if (fs.existsSync(packageJsonPath)) {
          const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));

          // 更新 package.json 内容
          const updatedPackageJson = {
            name: "xdc-components",
            version: "1.0.0",
            description: "常用组件库",
            main: "xdc-components.js",
            module: "xdc-components.js",
            types: "xdc-components.d.ts",
            files: [
              "xdc-components.js",
              "xdc-components.css",
              "xdc-components.umd.cjs",
              "xdc-components.d.ts"
            ],
            scripts: {
              "test": "echo \"Error: no test specified\" && exit 1"
            },
            keywords: [
              "vue",
              "vue3",
              "components",
              "ui",
              "ant-design-vue",
              "vxe-table"
            ],
            author: "",
            license: "ISC",
            peerDependencies: {
              "vue": "^3.5.0",
              "pinia": "^2.0.0"
            },
            dependencies: {
              "ant-design-vue": "^4.2.6",
              "lodash-es": "^4.17.21",
              "vxe-pc-ui": "^4.7.25",
              "vxe-table": "^4.14.6"
            }
          };

          fs.writeFileSync(targetPackagePath, JSON.stringify(updatedPackageJson, null, 2));
          console.log('✅ package.json copied to xdc-components/');
        }

        // 复制 README.md
        const readmePath = path.resolve(__dirname, 'README.md');
        const targetReadmePath = path.resolve(__dirname, 'xdc-components/README.md');

        if (fs.existsSync(readmePath)) {
          fs.copyFileSync(readmePath, targetReadmePath);
          console.log('✅ README.md copied to xdc-components/');
        }
      } catch (error) {
        console.error('❌ Failed to copy files:', error);
      }
    }
  };
};

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue(), copyFiles()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    }
  },

  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        // 如果需要自定义主题变量
        modifyVars: {
          // 在这里可以覆盖ant-design-vue的变量
        }
      }
    }
  },

  build: {
    outDir: "xdc-components", //输出文件名称，名称是自定义的
    lib: {
      entry: path.resolve(__dirname, "./src/components/index.ts"), // 改为新的入口文件
      name: "XdcComponents",
      fileName: "xdc-components",
    },
    rollupOptions: {
      external: ["vue", 'pinia', 'ant-design-vue', 'lodash-es', 'dayjs', 'vxe-table', 'vxe-pc-ui'],
      output: {
        // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
        globals: {
          vue: "Vue",
          'ant-design-vue': 'antd',
          'pinia': 'Pinia',
          'lodash-es': '_',
          'dayjs': 'dayjs',
          'vxe-table': 'VXETable',
          'vxe-pc-ui': 'VXETable'
        },
        // 确保CSS文件被正确输出
        assetFileNames: (assetInfo) => {
          if (assetInfo.name && assetInfo.name.endsWith('.css')) {
            return 'xdc-components.css';
          }
          return assetInfo.name || 'asset';
        }
      }
    },
    // 确保样式被包含在构建中
    cssCodeSplit: false
  }
})
