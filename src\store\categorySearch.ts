import { defineStore } from 'pinia';

// 定义过滤器类型
export interface FilterType {
    id: string;
    label: string;
    type: string;
    value?: any;
    options?: any[];
    format?: string;
    treeData?: any[];
    fieldNames?: Record<string, string>;
    placeholder?: string;
    showSearch?: boolean;
    multiple?: boolean;
    min?: number;
    max?: number;
    maxDays?: number;
}

// 定义列类型
export interface ColumnType {
    title: string;
    dataIndex?: string;
    key?: string;
    fixed?: 'left' | 'right' | false;
    checked?: boolean;
    [key: string]: any;
}

const getUserKey = () => {
    return 'XDC_search';
};

export const useCategorySearchStore = defineStore('categorySearch', {
    state: () => ({
        // 存储所有组件的搜索条件和表头设置
        searchFilters: [] as Array<{
            storageKey: string;
            filters: FilterType[];
            columns: ColumnType[];
        }>
    }),

    actions: {
        // 保存搜索条件和表头设置
        saveSearchFilters(storageKey: string, filters: FilterType[]) {
            const index = this.searchFilters.findIndex(item => item.storageKey === storageKey);
            if (index > -1) {
                this.searchFilters[index].filters = filters;
            } else {
                this.searchFilters.push({
                    storageKey,
                    filters,
                    columns: []
                });
            }
            localStorage.setItem(getUserKey(), JSON.stringify(this.searchFilters));
        },

        // 加载搜索条件和表头设置
        loadSearchFilters(storageKey: string) {
            // 从 localStorage 恢复数据
            const stored = localStorage.getItem(getUserKey());
            if (stored) {
                try {
                    this.searchFilters = JSON.parse(stored);
                } catch (e) {
                    console.error('Failed to parse stored search filters', e);
                }
            }

            const componentData = this.searchFilters.find(item => item.storageKey === storageKey);
            return {
                filters: componentData?.filters || [],
                columns: componentData?.columns || []
            };
        },

        // 单独保存表头设置
        saveColumns(storageKey: string, columns: ColumnType[]) {
            const index = this.searchFilters.findIndex(item => item.storageKey === storageKey);
            if (index > -1) {
                this.searchFilters[index].columns = columns;
            } else {
                this.searchFilters.push({
                    storageKey,
                    filters: [],
                    columns,
                });
            }
            localStorage.setItem(getUserKey(), JSON.stringify(this.searchFilters));
        },

        // 清除搜索条件和表头设置
        clearSearchFilters(storageKey: string) {
            const index = this.searchFilters.findIndex(item => item.storageKey === storageKey);
            if (index > -1) {
                this.searchFilters.splice(index, 1);
                localStorage.setItem(getUserKey(), JSON.stringify(this.searchFilters));
            }
        },

        // 清除所有搜索条件和表头设置
        clearAllSearchFilters() {
            this.searchFilters = [];
            localStorage.removeItem(getUserKey());
        }
    }
});