<template>
  <a-upload :accept="accept" :before-upload="handleBeforeUpload" :show-upload-list="false" :multiple="isMultiple"
    :max-count="limit" :disabled="disabled">
    <template v-if="btnType === 'button'">
      <a-button v-bind="btnProps">
        <template v-if="icon !== null" #icon>
          <component :is="icon" v-if="icon" />
          <UploadOutlined v-else />
        </template>
        <slot>
          {{ btnTxt || '上传' }}
        </slot>
      </a-button>
    </template>
    <template v-else>
      <a v-bind="btnProps" :class="['upload-link', btnProps.class]" :style="btnProps.style" @click.prevent>
        <template v-if="icon !== null">
          <component :is="icon" v-if="icon" />
          <UploadOutlined v-else />
        </template>
        <slot>
          {{ btnTxt || '上传' }}
        </slot>
      </a>
    </template>
  </a-upload>
  <!-- 仅多选时显示文件名列表，并支持删除 -->
  <ul v-if="isMultiple && selectedFiles.length" style="margin-top: 8px;">
    <li v-for="file in selectedFiles" :key="file.uid || file.name" style="display: flex; align-items: center;">
      <span style="flex:1;">{{ file.name }}</span>
      <a style="margin-left: 8px; color: #ff4d4f; cursor: pointer;" @click="handleRemove(file)">
        <DeleteOutlined />
      </a>
    </li>
  </ul>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { UploadOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import type { UploadFile } from 'ant-design-vue';

defineOptions({ name: 'FileUpload' });


const props = defineProps({
  accept: { type: String, default: '' },
  btnTxt: { type: String, default: '' },
  btnType: { type: String, default: 'button' },
  btnProps: { type: Object, default: () => ({}) },
  limit: { type: Number, default: 1 }, // 默认单文件
  multiple: { type: Boolean, default: undefined }, // 由 limit 控制
  disabled: { type: Boolean, default: false },
  icon: { type: [Object, Function, null], default: undefined },
  fileSizeLimit: { type: Number, default: 5242880 },
});

const emit = defineEmits(['select', 'exceed']);

// 由 limit 自动决定 multiple
const isMultiple = computed(() => {
  if (props.multiple !== undefined) return props.multiple;
  return props.limit > 1;
});

// 维护已选文件列表
const selectedFiles = ref<UploadFile[]>([]);

const handleBeforeUpload = (file: UploadFile) => {
  if (props.fileSizeLimit && file.size > props.fileSizeLimit) {
    const msg = `文件大小不能超过${(props.fileSizeLimit / 1024 / 1024).toFixed(2)}MB`;
    message.error(msg);
    emit('exceed', file);
    return false;
  }
  if (isMultiple.value) {
    // 累加且去重，限制数量
    const exists = selectedFiles.value.find(f => f.name === file.name && f.size === file.size);
    if (!exists) {
      if (selectedFiles.value.length < props.limit) {
        selectedFiles.value.push(file);
        emit('select', [...selectedFiles.value]);
      } else {
        message.warning(`最多只能选择${props.limit}个文件`);
      }
    }
  } else {
    selectedFiles.value = [file];
    emit('select', file);
  }
  return false;
};

const handleRemove = (file: UploadFile) => {
  selectedFiles.value = selectedFiles.value.filter(f => f !== file);
  emit('select', [...selectedFiles.value]);
};
</script>