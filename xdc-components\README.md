# xdc-components

Vue 3 组件库，包含常用的业务组件。

## 组件列表

- **CategorySearch**: 分类搜索组件
- **BaseModal**: 模态框组件

## CategorySearch 分类搜索组件

一个功能强大的分类搜索组件，支持多种筛选类型和列设置功能。

### 基本用法

```vue
<template>
  <CategorySearch
    :loading="loading"
    :filter-types="filterTypes"
    :columns="columns"
    @search="handleSearch"
  />
</template>

<script setup>
import { CategorySearch } from 'xdc-components';

const filterTypes = [
  {
    id: 'name',
    label: '名称',
    type: 'input',
    placeholder: '请输入名称'
  },
  {
    id: 'status',
    label: '状态',
    type: 'select',
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  }
];

const columns = [
  { title: '名称', dataIndex: 'name' },
  { title: '状态', dataIndex: 'status' }
];
</script>
```

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| filterValue | 默认的筛选条件 | object | {} |
| loading | 加载状态 | boolean | - |
| filterTypes | 筛选类型配置 | array | [] |
| placeholder | 输入框占位符 | string | '点击选择搜索条件' |
| storageKey | 存储键，如果提供则使用，否则自动生成 | string | '' |
| hideRefresh | 是否隐藏刷新按钮 | boolean | false |
| showSwitch | 是否显示切换按钮 | boolean | false |
| showSave | 是否显示保存按钮 | boolean | false |
| showSettingColumns | 是否显示列设置 | boolean | true |
| columns | 表格列配置 | array | [] |
| disabledColumns | 禁用的列 | array | ['id', 'name'] |
| isDefaultSearch | 是否默认执行搜索 | boolean | true |

### 存储键生成规则

组件会根据以下规则生成存储键：

1. **如果提供了 `storageKey`**：直接使用提供的值
2. **如果没有提供 `storageKey`**：自动生成基于页面路径和配置的唯一键

#### 自动生成存储键

当没有提供 `storageKey` 时，组件会自动生成一个基于以下信息的唯一存储键：
- 当前页面路径
- 筛选类型配置
- 表格列配置

生成的键格式为：`category_search_auto_${hash}`

这样可以确保每个页面和配置组合都有唯一的存储键，避免数据冲突。

### 在 Vue Router 项目中使用

```vue
<template>
  <!-- 使用路由名称作为存储键 -->
  <CategorySearch
    :storage-key="`search-${$route.name}`"
    :loading="loading"
    :filter-types="filterTypes"
    @search="handleSearch"
  />
</template>
```

### 在非路由项目中使用

```vue
<template>
  <CategorySearch
    storage-key="user-list-search"
    :loading="loading"
    :filter-types="filterTypes"
    @search="handleSearch"
  />
</template>
```

### 使用自动生成存储键

```vue
<template>
  <!-- 不提供 storageKey，自动生成 -->
  <CategorySearch
    :loading="loading"
    :filter-types="filterTypes"
    :columns="columns"
    @search="handleSearch"
  />
</template>
```

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| search | 搜索事件 | (params: object) |
| switch | 切换事件 | (filters: array) |
| clear | 清空事件 | - |
| reset | 重置事件 | - |
| columnsChange | 列变化事件 | (columns: array) |
| update:columns | 列更新事件 | (columns: array) |
| update:filterValue | 筛选值更新事件 | (value: object) |

### 支持的筛选类型

- `input`: 文本输入
- `textarea`: 多行文本
- `select`: 下拉选择
- `cascader`: 级联选择
- `treeSelect`: 树形选择
- `checkbox`: 多选框
- `radio`: 单选框
- `date`: 日期选择
- `datetime`: 日期时间选择
- `dateRange`: 日期范围
- `datetimeRange`: 日期时间范围
- `time`: 时间选择
- `numberRange`: 数字范围
- `rate`: 评分
- `slider`: 滑块
- `switch`: 开关
- `solt`: 自定义插槽

## BaseModal 模态框组件

一个增强的模态框组件，支持模态框管理和全局控制。

### 基本用法

```vue
<template>
  <BaseModal
    v-model="visible"
    title="标题"
    :width="600"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <p>模态框内容</p>
  </BaseModal>
</template>

<script setup>
import { BaseModal } from 'xdc-components';

const visible = ref(false);

const handleOk = () => {
  console.log('确认');
  visible.value = false;
};

const handleCancel = () => {
  console.log('取消');
  visible.value = false;
};
</script>
```

### 使用模态框管理

```vue
<template>
  <BaseModal
    v-model="visible"
    modal-id="user-modal"
    title="用户信息"
  >
    <p>用户信息内容</p>
  </BaseModal>
</template>

<script setup>
import { BaseModal, useModalManager } from 'xdc-components';

const { closeAllModals } = useModalManager();

// 关闭所有模态框
const closeAll = () => {
  closeAllModals();
};
</script>
```

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 是否显示模态框 | boolean | false |
| open | 是否显示模态框（与 modelValue 相同） | boolean | false |
| modalId | 模态框唯一标识，用于模态框管理 | string | - |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:modelValue | 模态框显示状态更新 | (value: boolean) |
| update:open | 模态框显示状态更新 | (value: boolean) |
| ok | 确认按钮点击事件 | (e: Event) |
| cancel | 取消按钮点击事件 | (e: Event) |
| after-close | 模态框关闭后事件 | - |

### 插槽

| 插槽名 | 说明 |
|--------|------|
| default | 模态框内容 |
| title | 标题内容 |
| footer | 底部内容 |
| closeIcon | 关闭图标 |

### 模态框管理 Hook

#### useModalManager

提供模态框管理功能。

```typescript
import { useModalManager } from 'xdc-components';

const { registerModal, unregisterModal, closeAllModals } = useModalManager();
```

**方法：**

- `registerModal(id: string, ref: ModalRef)`: 注册模态框
- `unregisterModal(id: string)`: 注销模态框  
- `closeAllModals()`: 关闭所有模态框

**全局事件：**

- `close-all-modals`: 关闭所有模态框的全局事件

```javascript
// 触发全局关闭事件
window.dispatchEvent(new Event('close-all-modals'));
```
# FileUpload 文件上传组件

基于 [ant-design-vue](https://www.antdv.com/) 的文件上传组件，支持多选、大小限制、数量限制、删除等功能。

## 安装

```bash
npm install xdc-components ant-design-vue
```

## 使用

```vue
<template>
  <FileUpload
    :limit="3"
    :fileSizeLimit="2 * 1024 * 1024"
    btnTxt="上传文件"
    @select="onSelect"
    @exceed="onExceed"
  />
</template>

<script setup>
import { FileUpload } from 'xdc-components';

function onSelect(files) {
  console.log('已选文件:', files);
}
function onExceed(file) {
  alert('文件超出大小限制: ' + file.name);
}
</script>
```

## Props

| 属性           | 说明               | 类型      | 默认值      |
| -------------- | ------------------ | --------- | ----------- |
| accept         | 接受的文件类型     | string    | ''          |
| btnTxt         | 按钮文字           | string    | '上传'      |
| btnType        | 按钮类型           | string    | 'button'    |
| btnProps       | 按钮属性           | object    | {}          |
| limit          | 最大文件数         | number    | 1           |
| multiple       | 是否多选           | boolean   | 自动由 limit 控制 |
| disabled       | 是否禁用           | boolean   | false       |
| icon           | 自定义图标         | 组件/函数/null | undefined |
| fileSizeLimit  | 单文件大小限制（字节）| number | 5242880     |

## 事件

| 事件名   | 说明               | 回调参数         |
| -------- | ------------------ | --------------- |
| select   | 文件选择后触发     | files/file      |
| exceed   | 文件超出大小限制时 | file            |


## 开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建
npm run build
```
