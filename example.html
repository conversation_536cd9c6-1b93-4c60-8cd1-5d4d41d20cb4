<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CategorySearch 自动生成存储键示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .example {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }

        .title {
            color: #1890ff;
            margin-bottom: 10px;
        }

        .description {
            color: #666;
            margin-bottom: 15px;
        }

        .code {
            background: #f6f8fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>CategorySearch 自动生成存储键功能</h1>

        <div class="example">
            <h3 class="title">功能说明</h3>
            <p class="description">
                当设置 <code>auto-generate-key="true"</code> 时，组件会自动生成一个基于页面路径和配置的唯一存储键。
                这样可以避免手动管理存储键名称，特别适合在多个页面使用相同组件的情况。
            </p>
        </div>

        <div class="example">
            <h3 class="title">基本用法</h3>
            <div class="code">
                &lt;template&gt;
                &lt;CategorySearch
                :auto-generate-key="true"
                :loading="loading"
                :filter-types="filterTypes"
                :columns="columns"
                @search="handleSearch"
                /&gt;
                &lt;/template&gt;
            </div>
        </div>

        <div class="example">
            <h3 class="title">生成的存储键格式</h3>
            <p class="description">
                自动生成的存储键格式为：<code>category_search_auto_${hash}</code>
            </p>
            <p class="description">
                hash 值基于以下信息生成：
            </p>
            <ul>
                <li>当前页面路径 (window.location.pathname)</li>
                <li>筛选类型配置 (filterTypes 的 id 组合)</li>
                <li>表格列配置 (columns 的 dataIndex/key 组合)</li>
            </ul>
        </div>

        <div class="example">
            <h3 class="title">查看存储键</h3>
            <p class="description">
                启用自动生成后，组件会显示一个信息图标 (ℹ️)，点击可以查看当前使用的存储键名称。
            </p>
        </div>

        <div class="example">
            <h3 class="title">存储键优先级</h3>
            <ol>
                <li>如果提供了 <code>storageKey</code>，直接使用</li>
                <li>如果启用了 <code>autoGenerateKey</code>，自动生成基于页面路径和配置的唯一键</li>
                <li>否则使用 <code>category_search_${routeName}</code> 格式</li>
            </ol>
        </div>

        <div class="example">
            <h3 class="title">使用场景</h3>
            <ul>
                <li><strong>多页面应用</strong>：不同页面使用相同组件，自动生成不同的存储键</li>
                <li><strong>动态配置</strong>：筛选类型或列配置变化时，自动生成新的存储键</li>
                <li><strong>避免冲突</strong>：无需手动管理存储键名称，避免命名冲突</li>
            </ul>
        </div>
    </div>
</body>

</html>