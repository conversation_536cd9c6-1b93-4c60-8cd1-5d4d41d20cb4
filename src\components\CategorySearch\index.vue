<template>
  <div class="category-search">

    <!-- Input 模式 -->
    <div v-if="type === 'input'" class="search-wrapper" @click="handleInputClick">
      <a-input ref="inputRef" v-model:value="searchText" :placeholder="placeholder" readonly :disabled="loading"
        allowClear>
        <template #prefix>
          <div class="tag-list">
            <div class="tag-list-inner">
              <SearchOutlined style="color: #999" />

              <a-tag v-for="(filter, index) in selectedFilters" :key="filter.id" closable class="tag-item"
                @close.stop="removeFilter(index)" @click.stop="editFilter(filter, index)">
                {{ filter.label }}: {{ formatFilterValue(filter) }}
              </a-tag>
            </div>
          </div>
        </template>
        <template #suffix>
          <CloseOutlined style="color: #999" v-if="selectedFilters?.length" @click.stop="reset" />
        </template>
      </a-input>
      <div class="action-group">
        <div v-if="!hideRefresh" class="action-border" :class="{ 'action-border-disabled': loading }"
          @click.stop="handleSearch" title="刷新">
          <ReloadOutlined />
        </div>

        <div class="action-border" v-if="showSwitch" @click.stop="handleSwitch" title="切换显示">
          <AppstoreOutlined />
        </div>

        <a-popover v-if="showSettingColumns" trigger="click" placement="bottomRight"
          :getPopupContainer="getPopupContainer" :mouseEnterDelay="0" :mouseLeaveDelay="0" @click.stop>
          <template #content>
            <div class="column-setting-content">
              <vxe-table style="color: #000" border="none" size="small" ref="tableRef" max-height="400"
                :row-config="{ drag: true }" :row-drag-config="rowDragConfig" :data="selectedColumns.slice(1)"
                @checkbox-all="onCheckAllChange" @row-dragend="handleDragEnd" :checkbox-config="{
                  checkField: 'checked',
                  checkStrictly: false,
                  checkMethod() {
                    return true;
                  },
                  visibleMethod() {
                    return false;
                  },
                }">
                <vxe-column type="checkbox" drag-sort width="30"> </vxe-column>
                <vxe-column field="title" title="title">
                  <template #header>
                    <div style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        width: 100%;
                      ">
                      <span>列展示/排序</span>
                      <a style="font-size: 15px" size="small" @click="handleColumnReset">重置</a>
                    </div>
                  </template>
                  <template #default="{ row }">
                    <div class="column-cell">
                      <span :style="{ opacity: row.checked ? 1 : 0.3, fontSize: '14px' }">{{
                        row.title
                      }}</span>
                      <a-space>
                        <PushpinOutlined class="action-icon" :class="{ active: row.fixed === 'left' }"
                          @click.stop="setColumnFixed(row, row.fixed === 'left' ? false : 'left')" title="固定到左侧" />
                        <PushpinOutlined class="action-icon" :class="{ active: row.fixed === 'right' }" :rotate="270"
                          @click.stop="setColumnFixed(row, row.fixed === 'right' ? false : 'right')" title="固定到右侧" />
                        <EyeInvisibleOutlined v-if="!row.checked" class="visibility-icon"
                          :class="{ disabled: isColumnDisabled(row) }"
                          @click.stop="!isColumnDisabled(row) && toggleColumnVisibility(row)" />
                        <EyeOutlined v-else class="visibility-icon" :class="{ disabled: isColumnDisabled(row) }"
                          @click.stop="!isColumnDisabled(row) && toggleColumnVisibility(row)" />
                      </a-space>
                    </div>
                  </template>
                </vxe-column>
              </vxe-table>
            </div>
          </template>
          <div class="action-border" title="筛选列表">
            <SettingOutlined />
          </div>
        </a-popover>

        <div class="action-border" v-if="showSave" @click.stop="handleSave" title="保存设置">
          <SaveOutlined />
        </div>
        <!-- 添加自定义操作按钮插槽 -->
        <div v-if="$slots.icon" @click.stop>
          <slot name="icon"></slot>
        </div>
      </div>
    </div>

    <!-- Form 模式 -->
    <div v-else-if="type === 'form'" class="form-search-wrapper">
      <a-row :gutter="[16, 16]">
        <a-col v-for="filter in availableFilterTypes" :key="filter.id" :span="formSpan">
          <!-- <div class="form-filter-item">
            <div class="filter-label">{{ filter.label }}:</div>
            <a-tag closable @close="removeFilter(selectedFilters.indexOf(filter))"
              @click="editFilter(filter, selectedFilters.indexOf(filter))">
              {{ filter.label }}
            </a-tag>
          </div> -->

          <!-- 数字范围 -->
          <div v-if="filter.type === 'numberRange'" class="form-item number-range">
            <a-input-number v-model:value="filterTemp.start" placeholder="最小值" :min="0" style="width: 120px"
              @pressEnter="handleConfirm" />
            <span class="separator">-</span>
            <a-input-number v-model:value="filterTemp.end" placeholder="最大值" :min="0" style="width: 120px"
              @pressEnter="handleConfirm" />
          </div>

          <!-- 选择类控件 -->
          <component v-else-if="['select', 'cascader', 'treeSelect'].includes(filter.type)"
            :is="`a-${filter.type === 'treeSelect' ? 'tree-select' : filter.type}`"
            v-model:value="filterTemp[filter.id]" :options="filter.options" :tree-data="filter.treeData"
            :placeholder="filter.placeholder || '请选择'" class="form-item" :getPopupContainer="getPopupContainer"
            @change="handleConfirm" v-bind="{
              ...(filter.showSearch ? { showSearch: true } : {}),
              ...(filter.multiple ? { mode: 'multiple' } : {}),
            }" :filterOption="filter.showSearch
              ? (input, option) => option.label?.toLowerCase().includes(input.toLowerCase())
              : undefined
              " :fieldNames="filter.fieldNames" />

          <!-- 多选 -->
          <component v-else-if="filter.type === 'checkbox'" is="a-checkbox-group" v-model:value="filterTemp.checkbox"
            :options="filter.options" class="form-item" />
          <!-- 单选 -->
          <component v-else-if="filter.type === 'radio'" is="a-radio-group" v-model:value="filterTemp.radio"
            :options="filter.options" class="form-item" @change="handleConfirm" />

          <!-- 评分和滑块 -->
          <component v-else-if="['rate', 'slider'].includes(filter.type)" :is="`a-${filter.type}`"
            v-model:value="filterTemp[filter.type]" v-bind="filter.type === 'slider'
              ? { min: filter.min || 0, max: filter.max || 100 }
              : {}
              " class="form-item" />

          <!-- 开关 -->
          <a-switch v-else-if="filter.type === 'switch'" v-model:value="filterTemp.switch" class="form-item" />

          <!-- 文本输入 -->
          <component v-else-if="['input', 'textarea'].includes(filter.type)" :is="`a-${filter.type}`"
            v-model:value="filterTemp[filter.type]" :placeholder="filter.placeholder || '请输入'"
            :rows="filter.type === 'textarea' ? 4 : undefined" class="form-item" @pressEnter="handleConfirm" />

          <!-- 日期时间选择器 -->
          <a-date-picker v-else-if="
            ['date', 'datetime', 'month', 'week', 'quarter', 'year'].includes(filter.type)
          " v-model:value="filterTemp.date" :format="filter.format || dateFormatMap[filter.type]"
            :show-time="filter.type === 'datetime'" :picker="filter.type === 'date' || filter.type === 'datetime'
              ? undefined
              : (filter.type as any)
              " :value-format="filter.format || dateFormatMap[filter.type]" class="form-item"
            :getPopupContainer="getPopupContainer" @change="handleConfirm" />

          <!-- 时间选择器 -->
          <a-time-picker v-else-if="filter.type === 'time'" v-model:value="filterTemp.time"
            :format="filter.format || 'HH:mm:ss'" class="form-item" :getPopupContainer="getPopupContainer"
            @change="handleConfirm" />

          <!-- 日期范围选择器 -->
          <a-range-picker v-else-if="filter.type === 'dateRange' || filter.type === 'datetimeRange'"
            v-model:value="filterTemp.dateRange as any" :format="filter.format ||
              (filter.type === 'datetimeRange' ? dateFormatMap.datetime : dateFormatMap.date)
              " :value-format="filter.format ||
                (filter.type === 'datetimeRange' ? dateFormatMap.datetime : dateFormatMap.date)
                " :show-time="filter.type === 'datetimeRange'" class="form-item" :getPopupContainer="getPopupContainer"
            @change="handleConfirm" :disabled-date="filter?.maxDays
              ? current => disabledDate(current, filter.maxDays)
              : null
              " @calendar-change="onCalendarChange" />
          <!-- 自定义插槽类型 -->
          <!-- <template v-if="currentFilter.type === 'slot'">
            <slot name="filter-slots" :filter="currentFilter" :filterTemp="filterTemp" :confirm="handleConfirm"></slot>
          </template> -->

          <template v-if="filter.type === 'slot'">
            <slot name="filter-slots" :filter="filter" :filterTemp="filterTemp" :confirm="handleConfirm"></slot>
          </template>


        </a-col>
      </a-row>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <a-button type="primary" @click="handleSearch" :loading="loading" :disabled="loading">
          <SearchOutlined />
          查询
        </a-button>
        <a-button @click="reset">
          <ReloadOutlined />
          重置
        </a-button>
        <a-button v-if="showSwitch" @click="handleSwitch">
          <AppstoreOutlined />
          切换显示
        </a-button>
        <a-button v-if="showSave" @click="handleSave">
          <SaveOutlined />
          保存设置
        </a-button>
        <!-- 自定义操作按钮插槽 -->
        <slot name="icon"></slot>
      </div>
    </div>

    <div v-if="visible" class="dropdown-panel" @click.stop>
      <template v-if="!currentFilter.type">
        <div class="type-list">
          <div v-for="type in availableFilterTypes" :key="type.id" class="type-item" @click="selectFilterType(type)">
            {{ type.label }}
          </div>
        </div>
      </template>

      <template v-else>
        <div class="form-content">
          <div class="form-header">
            <a-button type="link" @click="backToTypes">
              <LeftOutlined /> 返回
            </a-button>
            <span>{{ currentFilter.label }}</span>
          </div>

          <!-- 数字范围 -->
          <div v-if="currentFilter.type === 'numberRange'" class="form-item number-range">
            <a-input-number v-model:value="filterTemp.start" placeholder="最小值" :min="0" style="width: 120px"
              @pressEnter="handleConfirm" />
            <span class="separator">-</span>
            <a-input-number v-model:value="filterTemp.end" placeholder="最大值" :min="0" style="width: 120px"
              @pressEnter="handleConfirm" />
          </div>

          <!-- 选择类控件 -->
          <component v-else-if="['select', 'cascader', 'treeSelect'].includes(currentFilter.type)"
            :is="`a-${currentFilter.type === 'treeSelect' ? 'tree-select' : currentFilter.type}`"
            v-model:value="filterTemp[currentFilter.id]" :options="currentFilter.options"
            :tree-data="currentFilter.treeData" :placeholder="currentFilter.placeholder || '请选择'" class="form-item"
            :getPopupContainer="getPopupContainer" @change="handleConfirm" v-bind="{
              ...(currentFilter.showSearch ? { showSearch: true } : {}),
              ...(currentFilter.multiple ? { mode: 'multiple' } : {}),
            }" :filterOption="currentFilter.showSearch
              ? (input, option) => option.label?.toLowerCase().includes(input.toLowerCase())
              : undefined
              " :fieldNames="currentFilter.fieldNames" />

          <!-- 多选 -->
          <component v-else-if="currentFilter.type === 'checkbox'" is="a-checkbox-group"
            v-model:value="filterTemp.checkbox" :options="currentFilter.options" class="form-item" />
          <!-- 单选 -->
          <component v-else-if="currentFilter.type === 'radio'" is="a-radio-group" v-model:value="filterTemp.radio"
            :options="currentFilter.options" class="form-item" @change="handleConfirm" />

          <!-- 评分和滑块 -->
          <component v-else-if="['rate', 'slider'].includes(currentFilter.type)" :is="`a-${currentFilter.type}`"
            v-model:value="filterTemp[currentFilter.type]" v-bind="currentFilter.type === 'slider'
              ? { min: currentFilter.min || 0, max: currentFilter.max || 100 }
              : {}
              " class="form-item" />

          <!-- 开关 -->
          <a-switch v-else-if="currentFilter.type === 'switch'" v-model:value="filterTemp.switch" class="form-item" />

          <!-- 文本输入 -->
          <component v-else-if="['input', 'textarea'].includes(currentFilter.type)" :is="`a-${currentFilter.type}`"
            v-model:value="filterTemp[currentFilter.type]" :placeholder="currentFilter.placeholder || '请输入'"
            :rows="currentFilter.type === 'textarea' ? 4 : undefined" class="form-item" @pressEnter="handleConfirm" />

          <!-- 日期时间选择器 -->
          <a-date-picker v-else-if="
            ['date', 'datetime', 'month', 'week', 'quarter', 'year'].includes(currentFilter.type)
          " v-model:value="filterTemp.date" :format="currentFilter.format || dateFormatMap[currentFilter.type]"
            :show-time="currentFilter.type === 'datetime'" :picker="currentFilter.type === 'date' || currentFilter.type === 'datetime'
              ? undefined
              : (currentFilter.type as any)
              " :value-format="currentFilter.format || dateFormatMap[currentFilter.type]" class="form-item"
            :getPopupContainer="getPopupContainer" @change="handleConfirm" />

          <!-- 时间选择器 -->
          <a-time-picker v-else-if="currentFilter.type === 'time'" v-model:value="filterTemp.time"
            :format="currentFilter.format || 'HH:mm:ss'" class="form-item" :getPopupContainer="getPopupContainer"
            @change="handleConfirm" />

          <!-- 日期范围选择器 -->
          <a-range-picker v-else-if="currentFilter.type === 'dateRange' || currentFilter.type === 'datetimeRange'"
            v-model:value="filterTemp.dateRange as any" :format="currentFilter.format ||
              (currentFilter.type === 'datetimeRange' ? dateFormatMap.datetime : dateFormatMap.date)
              " :value-format="currentFilter.format ||
                (currentFilter.type === 'datetimeRange' ? dateFormatMap.datetime : dateFormatMap.date)
                " :show-time="currentFilter.type === 'datetimeRange'" class="form-item"
            :getPopupContainer="getPopupContainer" @change="handleConfirm" :disabled-date="currentFilter?.maxDays
              ? current => disabledDate(current, currentFilter.maxDays)
              : null
              " @calendar-change="onCalendarChange" />
          <!-- 自定义插槽类型 -->
          <!-- <template v-if="currentFilter.type === 'slot'">
            <slot name="filter-slots" :filter="currentFilter" :filterTemp="filterTemp" :confirm="handleConfirm"></slot>
          </template> -->

          <template v-if="currentFilter.type === 'slot'">
            <slot name="filter-slots" :filter="currentFilter" :filterTemp="filterTemp" :confirm="handleConfirm"></slot>
          </template>
          <div class="form-footer">
            <a-button type="primary" size="small" @click="handleConfirm" v-if="
              ![
                'select',
                'cascader',
                'treeSelect',
                'date',
                'datetime',
                'month',
                'week',
                'quarter',
                'year',
                'time',
                'dateRange',
                'datetimeRange',
                'slot',
              ].includes(currentFilter.type)
            ">确定</a-button>
            <!-- <a-button size="small" @click="handleCancel">取消</a-button> -->
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick, toRefs, watch } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { debounce, throttle } from 'lodash-es';
import { nanoid } from 'nanoid';

import {
  SearchOutlined,
  CloseOutlined,
  ReloadOutlined,
  AppstoreOutlined,
  SettingOutlined,
  SaveOutlined,
  PushpinOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  LeftOutlined,
  PlusOutlined
} from '@ant-design/icons-vue';


import { useCategorySearchStore } from '@/store/categorySearch';

defineOptions({ name: 'CategorySearch' });


const categorySearchStore = useCategorySearchStore();

// 日期格式映射
const dateFormatMap = {
  date: 'YYYY-MM-DD',
  datetime: 'YYYY-MM-DD HH:mm:ss',
  month: 'YYYY-MM',
  week: 'YYYY-[W]ww',
  quarter: 'YYYY-[Q]Q',
  year: 'YYYY',
  dateRange: 'YYYY-MM-DD',
  datetimeRange: 'YYYY-MM-DD HH:mm:ss',
};
const props = withDefaults(
  defineProps<{
    /**
     * 组件类型：input - 输入框模式，form - 表单平铺模式
     */
    type?: 'input' | 'form';
    /**
     * 表单模式下每列的宽度（24栅格系统）
     */
    formSpan?: number;
    /**
     * 默认的条件
     */
    filterValue?: any;
    loading: boolean;
    filterTypes: any[];
    placeholder?: string;
    storageKey?: string;
    hideRefresh?: boolean;
    showSwitch?: boolean;
    showSave?: boolean;
    showSettingColumns?: boolean;
    columns?: any[];
    disabledColumns?: string[];
    isDefaultSearch?: boolean;
    /**
     * 组件实例唯一标识（可选，区分同一页面多个组件）
     */
  }>(),
  {
    type: 'input',
    formSpan: 8,
    placeholder: '点击选择搜索条件',
    storageKey: '',
    showSettingColumns: true,
    columns: () => [],
    disabledColumns: () => ['id', 'name'],
    //默认进行搜索
    isDefaultSearch: true,
    filterValue: () => ({}),
  },
);

const emit = defineEmits([
  'search',
  'switch',
  'clear',
  'reset',
  'columnsChange',
  'update:columns',
  'update:filterValue',
]);

const visible = ref(false);
const currentFilter = ref<{
  type?: string;
  label?: string;
  id?: string;
  options?: any[];
  treeData?: any[];
  placeholder?: string;
  showSearch?: boolean;
  multiple?: boolean;
  fieldNames?: any;
  format?: string;
  min?: number;
  max?: number;
  maxDays?: number;
}>({});
/**
 * 筛选条件临时数据
 */
const filterTemp = reactive<{
  start?: number;
  end?: number;
  select?: any;
  checkbox?: any[];
  radio?: any;
  cascader?: any[];
  treeSelect?: any;
  rate?: number;
  slider?: number;
  switch?: boolean;
  input?: string;
  textarea?: string;
  date?: any;
  time?: any;
  dateRange?: any[];
  slot?: any;
  [key: string]: any;
}>({});
const selectedFilters = ref<any[]>([]);
const editingIndex = ref(-1);
const searchText = ref('');
const inputRef = ref<any>(null);

const columnSettingVisible = ref(false);
const selectedColumns = ref<any[]>([]);
const originalColumns = ref<any[]>([]);

const dates = ref<any>();

const onCalendarChange = val => {
  if (val && Array.isArray(val)) {
    dates.value = val;
  } else {
    dates.value = null;
  }
};

const disabledDate = (current, maxDays) => {
  if (!dates.value || dates.value.length === 0) {
    return false;
  }
  const tooLate = dates.value[0] && current.diff(dayjs(dates.value[0]), 'days') > maxDays;
  const tooEarly = dates.value[1] && dayjs(dates.value[1]).diff(current, 'days') > maxDays - 1;
  return tooEarly || tooLate;
};

const setColumnFixed = (column, fixed) => {
  column.fixed = fixed;

  // 重新排序列
  if (fixed) {
    const columns = selectedColumns.value;
    const currentIndex = columns.findIndex(col => col === column);
    columns.splice(currentIndex, 1);

    if (fixed === 'left') {
      // 左固定
      const lastLeftFixedIndex = columns.findIndex(col => col.fixed !== 'left');
      columns.splice(lastLeftFixedIndex === -1 ? 1 : lastLeftFixedIndex, 0, column);
    } else {
      // 右固定
      const firstRightFixedIndex = columns.findIndex(col => col.fixed === 'right');
      if (firstRightFixedIndex === -1) {
        columns.push(column);
      } else {
        columns.splice(firstRightFixedIndex, 0, column);
      }
    }
  }

  handleColumnChange();
};
// 添加判断方法
const isColumnDisabled = column => {
  return props.disabledColumns.includes(column.dataIndex || column.key);
};
const handleColumnChange = () => {
  const allColumns = selectedColumns.value;
  const visibleColumns = allColumns.filter(col => col.checked).map(({ checked, ...rest }) => rest);
  emit('update:columns', visibleColumns);
  const key = getStorageKey();
  categorySearchStore.saveColumns(key, allColumns);
};

// 初始化列设置
const initColumns = () => {
  // 从存储中获取列设置
  const key = getStorageKey();
  const { columns: savedColumns } = categorySearchStore.loadSearchFilters(key);

  if (savedColumns && savedColumns.length > 0) {
    selectedColumns.value = savedColumns.map(col => ({
      ...col,
      checked: col.checked !== undefined ? col.checked : true,
    }));
  } else {
    selectedColumns.value = props.columns.map(col => ({
      ...col,
      checked: true,
    }));
  }
};

const handleDragEnd = ({ newRow, oldRow, dragPos }) => {
  if (!newRow || !oldRow) return;

  const oldIndex = selectedColumns.value.findIndex(item => item.title === oldRow.title);
  const newIndex = selectedColumns.value.findIndex(item => item.title === newRow.title);
  if (oldIndex === -1 || newIndex === -1) return;

  if (oldIndex === newIndex) return;

  // 移动数组元素
  const [movedItem] = selectedColumns.value.splice(oldIndex, 1);

  // 根据 dragPos 确定插入位置
  let insertIndex;
  if (dragPos === 'top') {
    if (oldIndex === newIndex - 1) {
      insertIndex = newIndex - 1;
    } else {
      insertIndex = newIndex;
    }
  } else if (dragPos === 'bottom') {
    insertIndex = newIndex + 1;
  }

  // 插入目标位置
  selectedColumns.value.splice(insertIndex, 0, movedItem);
  handleColumnChange();
};
const handleColumnReset = () => {
  selectedColumns.value = originalColumns.value.map(col => ({
    ...col,
    checked: true,
  }));

  emit('update:columns', originalColumns.value);

  // 清除本地存储
  const key = getStorageKey();
  categorySearchStore.saveColumns(key, []);
};

const indeterminate = ref(false);

const rowDragConfig = {
  disabledMethod({ row }) {
    return row.fixed === 'left' || row.fixed === 'right' || isColumnDisabled(row);
  },
  async dragEndMethod({ newRow, oldRow, dragPos }) {
    // 获取前固定列的最后一个索引和后固定列的第一个索引
    const lastFixedLeftIndex = selectedColumns.value.findLastIndex(col => col.fixed === 'left');
    const firstFixedRightIndex = selectedColumns.value.findIndex(col => col.fixed === 'right');

    // 计算可拖动范围
    const firstNonFixedIndex = lastFixedLeftIndex === -1 ? 0 : lastFixedLeftIndex + 1;
    const lastNonFixedIndex = selectedColumns.value.length - 1;

    const oldIndex = selectedColumns.value.findIndex(item => item.title === oldRow.title);
    const newIndex = selectedColumns.value.findIndex(item => item.title === newRow.title);

    // 如果目标行是右固定列，则取消拖动
    if (newRow.fixed === 'right') {
      return false;
    }

    const targetIndex = dragPos === 'bottom' ? newIndex + 1 : newIndex;

    if (targetIndex < firstNonFixedIndex) {
      return false;
    }

    const [movedItem] = selectedColumns.value.splice(oldIndex, 1);
    selectedColumns.value.splice(targetIndex, 0, movedItem);
    handleColumnChange();
    return true;
  },
};

// 处理全选
const onCheckAllChange = ({ checked }) => {
  const editableColumns = selectedColumns.value.slice(1).filter(col => !isColumnDisabled(col));
  editableColumns.forEach(col => {
    col.checked = checked;
  });

  nextTick(() => {
    const $table = tableRef.value;
    if ($table) {
      const checkedRows = selectedColumns.value.filter(col => col.checked || isColumnDisabled(col));
      $table.setAllCheckboxRow(false);
      checkedRows.forEach(row => {
        $table.setCheckboxRow(row, true);
      });
    }
  });

  handleColumnChange();
};
const tableRef = ref();
// 切换列的可见性
const toggleColumnVisibility = column => {
  if (isColumnDisabled(column)) return;

  column.checked = !column.checked;
  nextTick(() => {
    const $table = tableRef.value;
    if ($table) {
      const checkedRows = selectedColumns.value.filter(col => col.checked || isColumnDisabled(col));
      $table.setAllCheckboxRow(false);
      checkedRows.forEach(row => {
        $table.setCheckboxRow(row, true);
      });
    }
  });
  handleColumnChange();
};

// 添加计算属性获取可用的筛选类型
const availableFilterTypes = computed(() => {
  const selectedIds = selectedFilters.value.map(f => f.id);
  return props.filterTypes.filter(type => !selectedIds.includes(type.id));
});

// 自动生成存储键名称
const generateStorageKey = () => {
  // 1. 组件名
  const componentName = 'CategorySearch';

  // 2. 页面路径
  let path = '';
  try {
    path = window.location.pathname + window.location.search + window.location.hash;
    if (!path || path === '/') {
      path = window.location.hostname || 'default';
    }
  } catch (e) {
    path = 'default';
  }

  // 3. 配置hash
  const filterIds = props.filterTypes.map(f => f.id).sort().join('_');
  const columnIds = props.columns.map(c => c.dataIndex || c.key).sort().join('_');
  const configHash = btoa(`${filterIds}_${columnIds}`).replace(/[^a-zA-Z0-9]/g, '').substring(0, 8);

  // 4. 组件实例唯一id
  // let instanceId = props.instanceId;
  // if (!instanceId) {
  //   // 生成后挂到 props 上，保证同一实例多次调用一致
  //   if (!(generateStorageKey as any)._autoInstanceId) {
  //     (generateStorageKey as any)._autoInstanceId = nanoid(6);
  //   }
  //   instanceId = (generateStorageKey as any)._autoInstanceId;
  // }

  // 5. 组合
  return `${componentName}_${path}_${configHash}`;
};

// 修改 存储键格式
const getStorageKey = () => {
  // 如果提供了 storageKey，直接使用；否则自动生成
  if (props.storageKey) {
    return props.storageKey;
  }
  return generateStorageKey();
};

const loadFromStorage = () => {
  const key = getStorageKey();
  const { filters, columns: savedColumns } = categorySearchStore.loadSearchFilters(key);
  selectedFilters.value = filters;

  // 如果有存储的表头设置，则使用存储的设置
  if (savedColumns && savedColumns.length > 0) {
    selectedColumns.value = savedColumns;
    const visibleColumns = savedColumns
      .filter(col => col.checked)
      .map(({ checked, ...rest }) => rest);
    emit('update:columns', visibleColumns);
  }
};

const handleInputClick = () => {
  currentFilter.value = {};
  editingIndex.value = -1;
  visible.value = !visible.value;
};

const handleGlobalClick = ({ target }) => {
  if (inputRef.value?.$el?.contains?.(target) || target.closest('.dropdown-menu')) return;
  visible.value = false;
  currentFilter.value = {};
};

onMounted(() => {
  originalColumns.value = [...props.columns];
  initColumns();
  document.addEventListener('click', handleGlobalClick);
  loadFromStorage();
});

onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick);
});

const selectFilterType = type => {
  currentFilter.value = { ...type } as any;
  if (currentFilter.value.showSearch === undefined) currentFilter.value.showSearch = false;
  if (currentFilter.value.multiple === undefined) currentFilter.value.multiple = false;
  Object.keys(filterTemp).forEach(key => delete filterTemp[key]);
  if (type.type === 'select') {
    filterTemp[type.id] = currentFilter.value.multiple ? [] : '';
  }
  // 时间范围类型，清空值和限制
  if (type.type === 'dateRange' || type.type === 'datetimeRange') {
    filterTemp.dateRange = null;
    dates.value = null;
  }
};

const backToTypes = () => {
  currentFilter.value = {};
};

const handleConfirm = () => {
  const filter = {
    ...currentFilter.value,
    value: { ...filterTemp },
  };
  if (editingIndex.value >= 0) {
    selectedFilters.value[editingIndex.value] = filter;
    editingIndex.value = -1;
  } else {
    selectedFilters.value.push(filter);
  }

  visible.value = false;
  currentFilter.value = {};
  Object.keys(filterTemp).forEach(key => delete filterTemp[key]);
  handleSearch();
};

const editFilter = (filter, index) => {
  currentFilter.value = { ...filter };
  editingIndex.value = index;

  Object.keys(filterTemp).forEach(key => delete filterTemp[key]);

  if (filter.type === 'dateRange' || filter.type === 'datetimeRange') {
    dates.value = null;
    filterTemp.dateRange = null;
    // 如果需要回显已选值，可以加上
    // Object.assign(filterTemp, filter.value);
  } else {
    Object.assign(filterTemp, filter.value);
  }

  visible.value = true;
};

const removeFilter = index => {
  if (index >= 0 && index < selectedFilters.value.length) {
    selectedFilters.value = selectedFilters.value.filter((_, i) => i !== index);
    nextTick(() => {
      handleSearch();
    });
  }
};
// 获取弹出容器
const getPopupContainer = triggerNode => triggerNode.parentNode;
// 格式化过滤器值
const formatFilterValue = filter => {
  if (!filter?.value) return '';
  const { type, value, options, format, treeData, fieldNames } = filter;
  // 日期时间类型处理
  if (['date', 'datetime', 'month', 'week', 'quarter', 'year', 'time'].includes(type)) {
    const dateValue = value.date || value[type];
    if (!dateValue) return '';
    return typeof dateValue === 'string'
      ? dateValue
      : dayjs(dateValue).format(format || dateFormatMap[type]);
  }
  // 日期范围和时间范围处理
  if (['dateRange', 'datetimeRange'].includes(type)) {
    const rangeValue = value.dateRange;
    if (!rangeValue || !Array.isArray(rangeValue)) return '';
    const [start, end] = rangeValue;
    if (!start || !end) return '';
    const formatStr = format || dateFormatMap[type];
    const formatValue = val => (typeof val === 'string' ? val : dayjs(val).format(formatStr));
    return `${formatValue(start)} ~ ${formatValue(end)}`;
  }

  // 数字范围处理
  if (type === 'numberRange') {
    const { start = '', end = '' } = value;
    return `${start} - ${end}`;
  }

  // 选择器类型处理
  if (type === 'select') {
    const option = options?.find(opt => opt.value === value.select);
    return option?.[fieldNames?.label || 'label'] || '';
  }

  if (type === 'radio') {
    return options?.find(opt => opt.value === value.radio)?.label || '';
  }

  if (type === 'checkbox') {
    return (
      value.checkbox
        ?.map(v => options?.find(opt => opt.value === v)?.label)
        .filter(Boolean)
        .join(', ') || ''
    );
  }

  if (type === 'cascader') {
    const findLabels = values => {
      const labels = [];
      let currentOptions = options;

      for (const value of values) {
        const option = currentOptions?.find(opt => opt.value === value);
        if (option) {
          labels.push(option.label);
          currentOptions = option.children;
        } else {
          labels.push(value);
        }
      }

      return labels;
    };

    return value.cascader ? findLabels(value.cascader).join(' / ') : '';
  }
  if (type === 'treeSelect') {
    const findLabel = (nodes, val) => {
      if (!nodes) return '';
      for (const node of nodes) {
        if (node.value === val) return node.title;
        if (node.children) {
          const found = findLabel(node.children, val);
          if (found) return found;
        }
      }
      return '';
    };
    return findLabel(treeData, value.treeSelect) || '';
  }
  if (type === 'slot') {
    // 处理slot类型，如果是对象则取label属性作为显示值
    const slotValue = value.slot;
    if (slotValue && typeof slotValue === 'object' && slotValue.label !== undefined) {
      return slotValue.label;
    }
    return slotValue || '';
  }
  // 基础类型处理
  const basicTypeMap = {
    rate: () => String(value.rate || ''),
    slider: () => String(value.slider || ''),
    switch: () => (value.switch ? '是' : '否'),
    input: () => value.input || '',
    textarea: () => value.textarea || '',
  };

  return basicTypeMap[type]?.() || '';
};
const { loading } = toRefs(props);

const typeValueKeyMap = {
  select: 'select',
  radio: 'radio',
  checkbox: 'checkbox',
  switch: 'switch',
  numberRange: 'start',
  dateRange: 'dateRange',
  datetimeRange: 'dateRange',
};

const handleSearch = throttle(async () => {
  if (loading.value) return;
  try {
    const searchParams = selectedFilters.value.reduce((params, filter) => {
      const { id, type, value } = filter;
      if (!id) return params;
      let v = value[id];
      if (v === undefined) v = value[type];
      if (v === undefined) v = value;
      params[id] = v;
      return params;
    }, {});

    function flattenSearchParams(params) {
      const result = {};
      for (const key in params) {
        const value = params[key];
        if (value && typeof value === 'object' && Array.isArray(value.dateRange)) {
          result[key] = value.dateRange;
        } else if (
          value &&
          typeof value === 'object' &&
          Object.keys(value).length === 1 &&
          Object.prototype.hasOwnProperty.call(value, 'date')
        ) {
          result[key] = value.date;
        }
        // 其他类型不变
        else {
          result[key] = value;
        }
      }
      return result;
    }
    const flatParams = flattenSearchParams(searchParams);
    emit('search', flatParams);
    emit('update:filterValue', searchParams); // 回显
  } catch (error) {
    console.error('Search failed:', error);
    message.error('搜索失败');
  }
}, 800);

function reset() {
  selectedFilters.value = [];
  loadFromStorage();
  handleSearch();
}

function handleSwitch() {
  emit('switch', selectedFilters.value);
}
// 保存到缓存
function handleSave() {
  const key = getStorageKey();
  categorySearchStore.saveSearchFilters(key, selectedFilters.value);
  message.success('保存成功');
}
const hasDefaultSearched = ref(false);
watch(
  [() => props.filterValue, () => props.filterTypes],
  () => {
    const conditionKeys = Object.keys(props.filterValue || {});
    const filters = [];
    for (const key of conditionKeys) {
      const findFilterItem = props.filterTypes.find(item => item.id === key);
      if (findFilterItem) {
        const value = props.filterValue[key];
        let filterValue;
        switch (findFilterItem.type) {
          case 'datetime':
          case 'numberRange':
          case 'datetimeRange':
          case 'quarter':
          case 'month':
          case 'week':
          case 'year':
            filterValue = value;
            break;
          default:
            filterValue = { [findFilterItem.type]: value };
        }

        filters.push({
          ...findFilterItem,
          value: filterValue,
        });
      }
    }
    selectedFilters.value = filters;
    if (props.isDefaultSearch && !hasDefaultSearched.value) {
      hasDefaultSearched.value = true;
      handleSearch();
    }
  },
  { immediate: true },
);

defineExpose({
  reset,
  search: handleSearch,
});
</script>

<style lang="less">
@import './style.less';
</style>
