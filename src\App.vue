<template>
  <a-row>
    <a-col :span="24">
      <!-- type="form" -->
      <CategorySearch type="form" style="width: 100%;" :filter-types="filterTypes" placeholder="请选择搜索条件"
        :show-save="true" :loading="listLoading" v-model:filterValue="searchParams" v-model:columns="tColumns"
        @search="handleSearch" @switch="handleSwitch" @clear="" @reset="">
      </CategorySearch>
    </a-col>
    <!-- <a-col :span="24">
      <FileUpload accept=".xls,.xlsx" btn-txt="上传" :icon="null" :btnProps="{ type: 'primary', size: 'large' }"
        @select="file => handleImportFile(file)" />
    </a-col> -->
  </a-row>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import CategorySearch from './components/CategorySearch/index.vue';

const handleImportFile = async (file, record) => {
  console.log('handleImportFile', file)
}

const listLoading = ref(false)
const searchParams = ref();
const tColumns = ref([])
const handleSearch = (params: any) => {
  searchParams.value = params;
  console.log('handleSearch', params)
}
const handleSwitch = () => {
  console.log('handleSwitch')
}
const filterTypes = [
  {
    label: '下拉框',
    id: 'status',
    type: 'select',
    options: [
      { label: '正常', value: 'normal' },
      { label: '异常', value: 'error' },
    ],
  },
  {
    id: 'name',
    label: '名称',
    type: 'input',
    placeholder: '请输入名称',
  },
  {
    id: 'name1',
    label: '名称1',
    type: 'input',
    placeholder: '请输入名称',
  }
]
</script>

<style scoped></style>
